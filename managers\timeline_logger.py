import mysql.connector
from typing import Optional
from os import getenv
from dotenv import load_dotenv
import logging

load_dotenv()

logger = logging.getLogger(__name__)

def _get_db_connection():
    """Get database connection"""
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )

def log_timeline_event(
    serial_number: Optional[str] = getenv("SERIAL_NUMBER"),
    entered_pin: Optional[str] = None,
    event_type: Optional[str] = None,
    event_result: Optional[str] = None,
    operator_id: Optional[int] = None,
    section_id: Optional[str] = None,
    tempered_unlock: Optional[int] = None,
    box_status: Optional[str] = None,
    message: Optional[str] = None,
    mode: Optional[str] = None,
    session_id: Optional[str] = None,
    order_number: Optional[str] = None,
    type: Optional[str] = None
):
    """
    Logs an event to the timeline_log table.
    """
    conn = _get_db_connection()
    cursor = conn.cursor()
    
    try:
        query = """
            INSERT INTO timeline_log (
                serial_number, entered_pin, event_type, event_result, operator_id, 
                section_id, tempered_unlock, box_status, message, mode, session_id
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        values = (
            serial_number, entered_pin, event_type, event_result, operator_id, 
            section_id, tempered_unlock, box_status, message, mode, session_id
        )
        cursor.execute(query, values)
        conn.commit()
        logger.info(f"Logged event: {event_type} for session {session_id}")
        return cursor.lastrowid
    except mysql.connector.Error as err:
        conn.rollback()
        logger.error(f"Database error logging timeline event: {err}")
        return None
    finally:
        cursor.close()
        conn.close()
